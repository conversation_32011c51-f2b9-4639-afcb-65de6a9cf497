class BikeBookingModel {
  final String? id;
  final String? pickupAddress;
  final String? dropAddress;
  final Map<String, dynamic>? pickupDetails;
  final Map<String, dynamic>? dropDetails;
  final double? distance;
  final double? fare;
  final String? rideType; // 'bike', 'car', 'auto'
  final String? status; // 'searching', 'found', 'booked', 'completed', 'cancelled'
  final String? paymentMethod;
  final DateTime? bookingTime;
  final DateTime? estimatedArrival;

  BikeBookingModel({
    this.id,
    this.pickupAddress,
    this.dropAddress,
    this.pickupDetails,
    this.dropDetails,
    this.distance,
    this.fare,
    this.rideType = 'bike',
    this.status = 'searching',
    this.paymentMethod = 'Wallet',
    this.bookingTime,
    this.estimatedArrival,
  });

  // Calculate fare based on distance (₹20 per km)
  double calculateFare() {
    if (distance == null || distance! <= 0) return 0.0;
    return distance! * 20.0;
  }

  // Check if pickup address is selected
  bool get hasPickupAddress => pickupAddress != null && pickupAddress!.isNotEmpty;

  // Check if drop address is selected
  bool get hasDropAddress => dropAddress != null && dropAddress!.isNotEmpty;

  // Check if both addresses are selected
  bool get hasCompleteAddresses => hasPickupAddress && hasDropAddress;

  // Get pickup address display text (full address)
  String get pickupAddressDisplay {
    return pickupAddress ?? '';
  }

  // Get drop address display text (full address)
  String get dropAddressDisplay {
    return dropAddress ?? '';
  }

  // Get pickup address title (short name)
  String get pickupAddressTitle {
    if (pickupDetails != null && pickupDetails!['title'] != null) {
      return pickupDetails!['title'];
    }
    return pickupAddress ?? '';
  }

  // Get drop address title (short name)
  String get dropAddressTitle {
    if (dropDetails != null && dropDetails!['title'] != null) {
      return dropDetails!['title'];
    }
    return dropAddress ?? '';
  }

  // Copy with method for immutable updates
  BikeBookingModel copyWith({
    String? id,
    String? pickupAddress,
    String? dropAddress,
    Map<String, dynamic>? pickupDetails,
    Map<String, dynamic>? dropDetails,
    double? distance,
    double? fare,
    String? rideType,
    String? status,
    String? paymentMethod,
    DateTime? bookingTime,
    DateTime? estimatedArrival,
  }) {
    return BikeBookingModel(
      id: id ?? this.id,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      dropAddress: dropAddress ?? this.dropAddress,
      pickupDetails: pickupDetails ?? this.pickupDetails,
      dropDetails: dropDetails ?? this.dropDetails,
      distance: distance ?? this.distance,
      fare: fare ?? this.fare,
      rideType: rideType ?? this.rideType,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      bookingTime: bookingTime ?? this.bookingTime,
      estimatedArrival: estimatedArrival ?? this.estimatedArrival,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pickupAddress': pickupAddress,
      'dropAddress': dropAddress,
      'pickupDetails': pickupDetails,
      'dropDetails': dropDetails,
      'distance': distance,
      'fare': fare,
      'rideType': rideType,
      'status': status,
      'paymentMethod': paymentMethod,
      'bookingTime': bookingTime?.toIso8601String(),
      'estimatedArrival': estimatedArrival?.toIso8601String(),
    };
  }

  // Create from JSON
  factory BikeBookingModel.fromJson(Map<String, dynamic> json) {
    return BikeBookingModel(
      id: json['id'],
      pickupAddress: json['pickupAddress'],
      dropAddress: json['dropAddress'],
      pickupDetails: json['pickupDetails'],
      dropDetails: json['dropDetails'],
      distance: json['distance']?.toDouble(),
      fare: json['fare']?.toDouble(),
      rideType: json['rideType'] ?? 'bike',
      status: json['status'] ?? 'searching',
      paymentMethod: json['paymentMethod'] ?? 'Wallet',
      bookingTime: json['bookingTime'] != null 
          ? DateTime.parse(json['bookingTime']) 
          : null,
      estimatedArrival: json['estimatedArrival'] != null 
          ? DateTime.parse(json['estimatedArrival']) 
          : null,
    );
  }
}

// Distance calculation result model
class DistanceResult {
  final double distanceInKm;
  final String distanceText;
  final String durationText;

  DistanceResult({
    required this.distanceInKm,
    required this.distanceText,
    required this.durationText,
  });

  factory DistanceResult.fromJson(Map<String, dynamic> json) {
    return DistanceResult(
      distanceInKm: json['distanceInKm']?.toDouble() ?? 0.0,
      distanceText: json['distanceText'] ?? '',
      durationText: json['durationText'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'distanceInKm': distanceInKm,
      'distanceText': distanceText,
      'durationText': durationText,
    };
  }
}
