import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../app/app.locator.dart';
import '../../../app/app.dialogs.dart';
import '../../../models/bike_booking_model.dart';
import '../../../models/address_model.dart';

import '../../../services/distance_service.dart' as distance_service;
import '../../../services/address_service.dart';
import '../map_picker/map_picker_view.dart';

class BikeBookingViewModel extends BaseViewModel {
  final DialogService _dialogService = locator<DialogService>();
  final NavigationService _navigationService = locator<NavigationService>();
  final AddressService _addressService = AddressService();
  final distance_service.DistanceService _distanceService = distance_service.DistanceService();

  // Current booking model
  BikeBookingModel _booking = BikeBookingModel();
  BikeBookingModel get booking => _booking;

  // Distance calculation result
  distance_service.DistanceResult? _distanceResult;
  distance_service.DistanceResult? get distanceResult => _distanceResult;

  // Current step in the booking process
  int _currentStep = 0; // 0: address selection, 1: ride search
  int get currentStep => _currentStep;

  // Loading states
  bool _isCalculatingDistance = false;
  bool get isCalculatingDistance => _isCalculatingDistance;

  bool _isSearchingRide = false;
  bool get isSearchingRide => _isSearchingRide;

  // Address getters
  bool get hasPickupAddress => _booking.hasPickupAddress;
  bool get hasDropAddress => _booking.hasDropAddress;
  bool get hasCompleteAddresses => _booking.hasCompleteAddresses;

  String get pickupAddressDisplay => _booking.pickupAddressDisplay;
  String get dropAddressDisplay => _booking.dropAddressDisplay;

  Map<String, dynamic>? get pickupDetails => _booking.pickupDetails;
  Map<String, dynamic>? get dropDetails => _booking.dropDetails;

  // Calculated fare
  double get calculatedFare {
    if (_distanceResult != null) {
      return _distanceResult!.distance * 20.0;
    }
    return 0.0;
  }

  // Handle pickup address selection
  Future<void> handlePickupAddressSelection() async {
    debugPrint('Bike Booking - Opening pickup address selection');
    await _handleAddressSelection(isPickup: true);
  }

  // Handle drop address selection
  Future<void> handleDropAddressSelection() async {
    debugPrint('Bike Booking - Opening drop address selection');
    await _handleAddressSelection(isPickup: false);
  }

  // Generic address selection handler
  Future<void> _handleAddressSelection({required bool isPickup}) async {
    debugPrint('Bike Booking - _handleAddressSelection method entry, isPickup: $isPickup');
    try {
      debugPrint('Bike Booking - _handleAddressSelection called with isPickup: $isPickup');
      setBusy(true);
      debugPrint('Bike Booking - Starting address selection, isPickup: $isPickup');

      // Get saved addresses
      debugPrint('Bike Booking - Getting saved addresses...');
      final savedAddresses = await _addressService.getSavedAddresses();
      debugPrint('Bike Booking - Found ${savedAddresses.length} saved addresses');

      if (savedAddresses.isEmpty) {
        // No saved addresses, go directly to map picker
        debugPrint('No saved addresses found, opening map picker');
        await _openMapPicker(isPickup: isPickup);
      } else {
        // Show address selection dialog
        debugPrint('Showing address selection dialog');
        final response = await _dialogService.showCustomDialog(
          variant: DialogType.addressSelection,
          title: 'Select Address',
          description: 'Choose from saved addresses or add new one',
        );

        if (response?.confirmed == true && response?.data != null) {
          debugPrint('Bike Booking - Dialog response data: ${response!.data}');
          final action = response.data['action'] as String?;
          debugPrint('Bike Booking - Dialog action: $action');

          if (action == 'select') {
            // Address selected from saved addresses
            final addressData = response.data['address'];
            debugPrint('Bike Booking - Selected address data: $addressData');
            final address = AddressModel.fromJson(addressData);
            await _setAddressFromModel(address, isPickup: isPickup);
          } else if (action == 'add_new') {
            // Add new address
            debugPrint('Bike Booking - Opening map picker for new address');
            await _openMapPicker(isPickup: isPickup);
          } else {
            debugPrint('Bike Booking - Unknown action: $action');
          }
        } else {
          debugPrint('Bike Booking - Dialog cancelled or no data returned');
        }
      }
    } catch (e, stackTrace) {
      debugPrint('Bike Booking - Error in address selection: $e');
      debugPrint('Bike Booking - Stack trace: $stackTrace');
      // Fallback to map picker
      await _openMapPicker(isPickup: isPickup);
    } finally {
      setBusy(false);
    }
  }

  // Open map picker for address selection
  Future<void> _openMapPicker({required bool isPickup}) async {
    try {
      final result = await _navigationService.navigateToView(
        const MapPickerView(),
      );

      if (result != null && result is Map<String, dynamic>) {
        final addressDetails = {
          'title': result['title'] ?? (isPickup ? 'Pickup Location' : 'Drop Location'),
          'fullAddress': result['address'],
          'latitude': result['latitude'],
          'longitude': result['longitude'],
          'contactName': '',
          'contactPhone': '',
        };

        if (isPickup) {
          _booking = _booking.copyWith(
            pickupAddress: result['address'],
            pickupDetails: addressDetails,
          );
          debugPrint('Bike Booking - Pickup address set from map: ${result['address']}');
        } else {
          _booking = _booking.copyWith(
            dropAddress: result['address'],
            dropDetails: addressDetails,
          );
          debugPrint('Bike Booking - Drop address set from map: ${result['address']}');
        }

        rebuildUi();

        // Calculate distance if both addresses are selected
        if (hasCompleteAddresses) {
          await _calculateDistance();
        }
      }
    } catch (e) {
      debugPrint('Error opening map picker: $e');
      _showErrorSnackBar('Failed to open map picker. Please try again.');
    }
  }

  // Set address from AddressModel
  Future<void> _setAddressFromModel(AddressModel address, {required bool isPickup}) async {
    final addressDetails = {
      'title': address.title,
      'fullAddress': address.fullAddress,
      'latitude': address.latitude,
      'longitude': address.longitude,
      'contactName': address.contactName,
      'contactPhone': address.contactPhone,
    };

    if (isPickup) {
      _booking = _booking.copyWith(
        pickupAddress: address.fullAddress, // Use full address for display
        pickupDetails: addressDetails,
      );
      debugPrint('Bike Booking - Pickup address set: ${address.fullAddress}');
    } else {
      _booking = _booking.copyWith(
        dropAddress: address.fullAddress, // Use full address for display
        dropDetails: addressDetails,
      );
      debugPrint('Bike Booking - Drop address set: ${address.fullAddress}');
    }

    rebuildUi();

    // Calculate distance if both addresses are selected
    if (hasCompleteAddresses) {
      await _calculateDistance();
    }
  }

  // Calculate distance between pickup and drop addresses
  Future<void> _calculateDistance() async {
    if (!hasCompleteAddresses) return;

    try {
      _isCalculatingDistance = true;
      rebuildUi();

      final pickupLat = _booking.pickupDetails!['latitude'];
      final pickupLng = _booking.pickupDetails!['longitude'];
      final dropLat = _booking.dropDetails!['latitude'];
      final dropLng = _booking.dropDetails!['longitude'];

      final result = await _distanceService.calculateRoadDistance(
        originLat: pickupLat,
        originLng: pickupLng,
        destinationLat: dropLat,
        destinationLng: dropLng,
      );

      _distanceResult = result;
      _booking = _booking.copyWith(
        distance: result.distance,
        fare: result.distance * 20.0,
      );

      debugPrint('Bike Booking - Distance calculated: ${result.distanceText}');
      debugPrint('Bike Booking - Fare calculated: ₹${calculatedFare.toStringAsFixed(0)}');
    } catch (e) {
      debugPrint('Error calculating distance: $e');
      _showErrorSnackBar('Failed to calculate distance. Please try again.');
    } finally {
      _isCalculatingDistance = false;
      rebuildUi();
    }
  }

  // Proceed to next step (ride search)
  void proceedToRideSearch() {
    if (!hasCompleteAddresses) {
      _showErrorSnackBar('Please select both pickup and drop addresses');
      return;
    }

    _currentStep = 1;
    _booking = _booking.copyWith(status: 'searching');
    _startRideSearch();
    rebuildUi();
  }

  // Start ride search simulation
  void _startRideSearch() {
    _isSearchingRide = true;
    rebuildUi();

    // Simulate ride search for 3-5 seconds
    Future.delayed(const Duration(seconds: 4), () {
      _isSearchingRide = false;
      _booking = _booking.copyWith(
        status: 'found',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 5)),
      );
      rebuildUi();
    });
  }

  // Go back to address selection
  void goBackToAddressSelection() {
    _currentStep = 0;
    _isSearchingRide = false;
    _booking = _booking.copyWith(status: 'searching');
    rebuildUi();
  }

  // Cancel ride search
  void cancelRideSearch() {
    _isSearchingRide = false;
    _booking = _booking.copyWith(status: 'cancelled');
    _navigationService.back();
  }

  // Book the ride
  void bookRide() {
    _booking = _booking.copyWith(
      status: 'booked',
      bookingTime: DateTime.now(),
    );
    _showSuccessSnackBar('Bike ride booked successfully!');
    // Navigate to booking confirmation or tracking screen
    _navigationService.back();
  }

  // Show error snackbar
  void _showErrorSnackBar(String message) {
    // Implementation depends on your snackbar service
    debugPrint('Error: $message');
  }

  // Show success snackbar
  void _showSuccessSnackBar(String message) {
    // Implementation depends on your snackbar service
    debugPrint('Success: $message');
  }
}
