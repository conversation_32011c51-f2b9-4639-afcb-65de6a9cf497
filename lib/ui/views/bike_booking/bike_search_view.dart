import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'bike_booking_viewmodel.dart';
import '../../common/app_colors.dart';

class BikeSearchView extends StatelessWidget {
  final BikeBookingViewModel viewModel;

  const BikeSearchView({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Top Section (Map Placeholder)
        _buildMapSection(),
        
        // Main Content
        Expanded(
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                // Status Section
                _buildStatusSection(),
                
                // Location Section
                _buildLocationSection(),
                
                // Payment Section
                _buildPaymentSection(),
                
                const Spacer(),
                
                // Action Buttons
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMapSection() {
    return Container(
      height: 300,
      width: double.infinity,
      decoration: const BoxDecoration(
        color: AppColors.lightBlueBackground,
      ),
      child: Stack(
        children: [
          // Google Map with pins and route
          ClipRRect(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(16),
            ),
            child: GoogleMap(
              initialCameraPosition: _getInitialCameraPosition(),
              markers: _buildMapMarkers(),
              polylines: _buildRoutePolyline(),
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              myLocationButtonEnabled: false,
              compassEnabled: false,
              onMapCreated: (GoogleMapController controller) {
                _fitMapToShowBothAddresses(controller);
              },
            ),
          ),
          
          // Back button
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: viewModel.goBackToAddressSelection,
                icon: const Icon(Icons.arrow_back, color: Colors.black87),
              ),
            ),
          ),
          

        ],
      ),
    );
  }

  CameraPosition _getInitialCameraPosition() {
    // Get coordinates from pickup address
    final pickupLat = viewModel.booking.pickupDetails?['latitude'] ?? 28.6139;
    final pickupLng = viewModel.booking.pickupDetails?['longitude'] ?? 77.2090;

    return CameraPosition(
      target: LatLng(pickupLat, pickupLng),
      zoom: 12.0,
    );
  }

  Set<Marker> _buildMapMarkers() {
    Set<Marker> markers = {};

    // Pickup marker (green)
    if (viewModel.booking.pickupDetails != null) {
      final pickupLat = viewModel.booking.pickupDetails!['latitude'];
      final pickupLng = viewModel.booking.pickupDetails!['longitude'];

      if (pickupLat != null && pickupLng != null) {
        markers.add(
          Marker(
            markerId: const MarkerId('pickup'),
            position: LatLng(pickupLat, pickupLng),
            icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
              title: 'Pickup Location',
              snippet: viewModel.booking.pickupAddressDisplay,
            ),
          ),
        );
      }
    }

    // Drop marker (red)
    if (viewModel.booking.dropDetails != null) {
      final dropLat = viewModel.booking.dropDetails!['latitude'];
      final dropLng = viewModel.booking.dropDetails!['longitude'];

      if (dropLat != null && dropLng != null) {
        markers.add(
          Marker(
            markerId: const MarkerId('drop'),
            position: LatLng(dropLat, dropLng),
            icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
            infoWindow: InfoWindow(
              title: 'Drop Location',
              snippet: viewModel.booking.dropAddressDisplay,
            ),
          ),
        );
      }
    }

    return markers;
  }

  Set<Polyline> _buildRoutePolyline() {
    Set<Polyline> polylines = {};

    // Create route line between pickup and drop
    if (viewModel.booking.pickupDetails != null && viewModel.booking.dropDetails != null) {
      final pickupLat = viewModel.booking.pickupDetails!['latitude'];
      final pickupLng = viewModel.booking.pickupDetails!['longitude'];
      final dropLat = viewModel.booking.dropDetails!['latitude'];
      final dropLng = viewModel.booking.dropDetails!['longitude'];

      if (pickupLat != null && pickupLng != null && dropLat != null && dropLng != null) {
        polylines.add(
          Polyline(
            polylineId: const PolylineId('route'),
            points: [
              LatLng(pickupLat, pickupLng),
              LatLng(dropLat, dropLng),
            ],
            color: Colors.black,
            width: 3,
            patterns: [],
          ),
        );
      }
    }

    return polylines;
  }

  void _fitMapToShowBothAddresses(GoogleMapController controller) {
    if (viewModel.booking.pickupDetails != null && viewModel.booking.dropDetails != null) {
      final pickupLat = viewModel.booking.pickupDetails!['latitude'];
      final pickupLng = viewModel.booking.pickupDetails!['longitude'];
      final dropLat = viewModel.booking.dropDetails!['latitude'];
      final dropLng = viewModel.booking.dropDetails!['longitude'];

      if (pickupLat != null && pickupLng != null && dropLat != null && dropLng != null) {
        final bounds = LatLngBounds(
          southwest: LatLng(
            pickupLat < dropLat ? pickupLat : dropLat,
            pickupLng < dropLng ? pickupLng : dropLng,
          ),
          northeast: LatLng(
            pickupLat > dropLat ? pickupLat : dropLat,
            pickupLng > dropLng ? pickupLng : dropLng,
          ),
        );

        controller.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 100.0),
        );
      }
    }
  }

  Widget _buildStatusSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.two_wheeler,
                  color: AppColors.primaryBlue,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      viewModel.isSearchingRide ? 'Searching for bike...' : 'Bike found!',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        fontFamily: 'Poppins',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      viewModel.isSearchingRide
                          ? 'Finding the best bike for you'
                          : 'Ready to pick you up',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontFamily: 'Poppins',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Loading/progress bar
          if (viewModel.isSearchingRide) ...[
            const LinearProgressIndicator(
              backgroundColor: AppColors.lightBorderColor,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
            ),
            const SizedBox(height: 8),
            const Text(
              'Searching for nearby riders...',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontFamily: 'Poppins',
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Text(
                'Rider found!',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.successColor,
                  fontFamily: 'Poppins',
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightBlueBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.1)),
      ),
      child: Column(
        children: [
          // Pickup location
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: AppColors.pickupIconColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Pickup',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Poppins',
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      viewModel.booking.pickupAddressDisplay,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Poppins',
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Dotted line
          Container(
            margin: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              children: [
                const SizedBox(width: 6),
                SizedBox(
                  width: 2,
                  height: 30,
                  child: CustomPaint(
                    painter: DottedLinePainter(),
                  ),
                ),
                const SizedBox(width: 10),
                if (viewModel.distanceResult != null) ...[
                  Expanded(
                    child: Text(
                      '${viewModel.distanceResult!.distanceText} • ${viewModel.distanceResult!.durationText}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                        fontFamily: 'Poppins',
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Drop location
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: AppColors.deliveryIconColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Drop',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Poppins',
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      viewModel.booking.dropAddressDisplay,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Poppins',
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadowColor,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              fontFamily: 'Poppins',
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.account_balance_wallet,
                  color: AppColors.primaryBlue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Wallet',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                        fontFamily: 'Poppins',
                      ),
                    ),
                    Text(
                      '₹${viewModel.calculatedFare.toStringAsFixed(0)}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryBlue,
                        fontFamily: 'Poppins',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: viewModel.cancelRideSearch,
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.grey[700],
                side: BorderSide(color: Colors.grey[300]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Book button (only show when ride is found)
          if (!viewModel.isSearchingRide) ...[
            Expanded(
              child: ElevatedButton(
                onPressed: viewModel.bookRide,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.successColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'Book Ride',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

// Custom painter for dotted line
class DottedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[400]!
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashHeight = 3.0;
    const dashSpace = 3.0;
    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
